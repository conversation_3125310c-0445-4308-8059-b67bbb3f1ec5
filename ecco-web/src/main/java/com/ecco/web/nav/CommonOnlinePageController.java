package com.ecco.web.nav;


import com.ecco.dao.ReferralRepository;
import com.ecco.dom.ReferralServiceRecipient;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.web.WebSlice;
import com.ecco.security.ReferenceDataImpl;
import com.ecco.service.ReferralService;
import com.ecco.servicerecipient.ServiceRecipientSummary;
import com.ecco.servicerecipient.ServiceRecipientSummaryService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.ecco.security.SecurityUtil.hasAuthority;

/** Note that these handlers are remapped in HttpRemapConfig. */
@Controller
@WebSlice("nav")
@AllArgsConstructor
public class CommonOnlinePageController {

    private final ApplicationProperties applicationProperties;

    private final ReferralRepository referralRepository;
    private final ReferralService referralService;
    private final ReferenceDataImpl referenceData;
    private final ServiceRecipientSummaryService serviceRecipientSummaryService;

    @ModelAttribute
    public ApplicationProperties applicationProperties() {
        return applicationProperties;
    }

    // NB Application.java includes WebMvcCommonConfig which includes CommonOnlinePageController
    // which is a bridge between newer spring-boot and ecco-war, so we move the /welcome mapping here
    // if we don't go to welcome.html, we miss the app redirect usage - and end up at nav/r/welcome/ via webSuccessHandler
    // NB the nav/r/welcome/ also maps to the same pages - eg nav/r/welcome/care, due to online/welcome loading WelcomeAppBar matching /care
    @GetMapping("/secure/welcome.html")
    public ModelAndView redirect(ModelMap model) {
        // Go directly to app if user doesn't have manager or staff functionality
        if (!hasAuthority("ROLE_STAFF") && !hasAuthority("ROLE_ADMIN")) {
            // go to dedicated pages - nav/r/care matches below 'online/care' hbs which loads importRequireJsModules="care/router"
            if (hasAuthority("ROLE_CARER")) {
                return new ModelAndView("redirect:/nav/r/care/", model);
            }
            // does not resolve - only nav/r/welcome/dailyChecks appears to resolve (in WelcomeAppBar)
            if (hasAuthority("ROLE_DAILYCHECKS")) {
                return new ModelAndView("redirect:/r/app/checks/", model);
            }
        }

        return new ModelAndView("redirect:/nav/r/welcome/", model);
    }

    // REACT-ROUTER SECTION STARTS
    /**
     * Specific matchers for react-router, more specific matches below.
     * e.g. matches nav/r/refer which goes to online/refer which loads referral/refer
     */
    @GetMapping({"/{:[rw]}/welcome/**"})
    public String welcome(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/welcome";
    }

    @GetMapping({"/r/main/**", "/w/main/**"})
    public String getMain(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/main";
    }

    @GetMapping({"/r/care/**", "/w/care/**", "/r/events/**", "/w/events/**"})
    public String getCare(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/care";
    }

    @GetMapping({"/r/buildings/**", "/w/buildings/**"}) // online/r/buildings/import/:externalSourceName/:externalRef
    public String getBuildings(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/buildings";
    }

    /**
     * Send any /offline/ request to handlebars/main.hbs which loads offline/router.tsx.
     * This allows us to get the debug version for offline code - this could be related to
     * debugging 'dev' mode in RarelyChangingStaticFileResourceProvider
     */
    @GetMapping("/r/offline/**")
    public String getOfflineRouter(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/main";
    }

    @GetMapping({"/r/dashboard/**", "/w/dashboard/**"})
    public String getDashboard(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/dashboard";
    }

    /**
     * Note: This is /nav/r/refer in case that's got links /p/r/refer os public one via PublicPageController
     * See also ecco.mvc.basePath in WebMvcCommonConfig.
     */
    @GetMapping({"/p/r/refer/**", "/r/refer/**", "/w/refer/**"})
    public String getRefer(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/refer";
    }

    // this is needed for war <context>/p/r/incident
    @GetMapping({"/p/r/incident", "/p/r/incident/**", "/r/incident/**", "/w/incident/**"})
    public String getIncident(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/incident";
    }

    // this is needed for war <context>/p/r/incident
    @GetMapping({"/p/r/repair", "/p/r/repair/**", "/r/repair/**", "/w/repair/**"})
    public String getRepair(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/repair";
    }

    // this is good for war, but not boot
    // <context>/p/r/guidance/...
    //      for war - '/r/guidance' is needed
    //          http://localhost:8888/ecco-war/nav|online/p/r/guidance/-form-/
    //      for boot - see PublicPageController
    //          http://localhost:8899/p/r/guidance/-form-/
    @GetMapping("/r/guidance/**")
    public String getPublicGuidanceCustomForm(ModelMap model, HttpServletRequest request) {
        return getGuidanceCustomForm(model, request);
    }

    // REACT-ROUTER SECTION ENDS


    @GetMapping({"/referrals/{rid}", "/referrals/{rid}/"})
    public void getNewReferralOverview(@PathVariable long rid, HttpServletRequest request, HttpServletResponse response) throws IOException {
        int srId = referralRepository.getServiceRecipientId(rid);
        getNewReferralOverviewBySrId(srId, request, response);
    }

    @GetMapping({"/referrals/sr/{srId}"})
    public void getNewReferralOverviewBySrId(@PathVariable int srId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.sendRedirect(request.getContextPath() + "/nav/r/main/sr2/" + srId + "/");
    }

    // http://localhost:8888/ecco-war/online/guidance/-form-/printable
    @GetMapping("/guidance/**")
    public String getGuidanceCustomForm(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/guidance";
    }

    /**
     * Gets the current evidence snapshot into a printable page.
     * eg url http://localhost:8080/ecco-war/online/form-snapshot/svcrec/200000?taskName=customForm1
     * <p>
     * This mimics evidence-work/history.jsp via the OnlinePageController url support-history/<type>/srId
     * but includes the referral bit from the grabSheet (as per beta/grabSheetDef via the OnlinePageController url referrals/<id>/grabSheet)
     * There is a third print-style page (as per online/referrals/handler.jsp via the OnlinePageController url referrals/<id>/task/<taskName>)
     */
    @GetMapping("/form-snapshot/{recipientType}/{serviceRecipientId}")
    public String getCustomFormSnapshot(@PathVariable int serviceRecipientId, ModelMap model, HttpServletRequest request) {
        populateModel(serviceRecipientId, model);
        model.addAttribute("requireJsModules", "evidence/evidence-snapshot-page referral/enhancePrintPage");
        referenceData.addReferenceDataToModel(model, request);
        return "online/printable";
    }

    // NB 'type' is used in evidence-history-page
    @GetMapping("/evidence-history/{type}/{serviceRecipientId}")
    public String getHistory(@PathVariable int serviceRecipientId, ModelMap model, HttpServletRequest request) {
        populateModel(serviceRecipientId, model);
        model.addAttribute("requireJsModules", "evidence/evidence-history-page referral/enhancePrintPage");
        referenceData.addReferenceDataToModel(model, request);
        return "online/printable";
    }

    @GetMapping("/service-recipient/{serviceRecipientId}/task/{taskName}/printable")
    public String getPrintableTask(@PathVariable int serviceRecipientId, ModelMap model, HttpServletRequest request) {
        populateModel(serviceRecipientId, model);
        model.addAttribute("requireJsModules", "referral/printable-task-page referral/enhancePrintPage");
        referenceData.addReferenceDataToModel(model, request);
        return "online/printable";
    }

    @GetMapping("/service-recipients/{srId}/grabSheet/")
    public String getReferralGrabSheet(@PathVariable int srId, ModelMap model, HttpServletRequest request) {
        populateModel(srId, model);
        model.addAttribute("requireJsModules", "service-recipients/grabsheet-page referral/enhancePrintPage");
        referenceData.addReferenceDataToModel(model, request);
        return "online/printable";
    }

    private void populateModel(int serviceRecipientId, ModelMap model) {
        var sr = serviceRecipientSummaryService.findAndVerifyAccess(serviceRecipientId);
        if (sr.discriminator.equals(ReferralServiceRecipient.DISCRIMINATOR)) {
            printableReferral(referralRepository.getReferralIdByServiceRecipientId(serviceRecipientId), model, "");
        } else {
            printableSr(sr, model, "");
        }
    }

    private void printableSr(ServiceRecipientSummary sr, ModelMap model, String title) {
        model.put("referralPrintPageTitleCode", title);
        model.put("displayName", sr.displayName);

        model.put("quickGuideId", "quickGuide");
        model.put("serviceRecipientId", sr.serviceRecipientId);

        model.put("hasOverview", true);

        model.put("contactId", sr.contactId);
        model.put("contactAddress", sr.address == null ? "" : sr.address.toCommaSepString());

        //model.put("avatarId", sr.contactId.getAvatarId());
    }

    private void printableReferral(long rid, ModelMap model, String title) {
        model.put("referralPrintPageTitleCode", title);
        ModelMap legacyMap = referralService.getLegacyReferralDetails(rid);
        model.addAllAttributes(legacyMap);
    }
}
