package com.ecco.webApi.config;

import com.ecco.webApi.security.WebApiAccessDeniedHandler;
import com.ecco.webApi.security.WebApiAuthenticationEntryPoint;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.header.HeaderWriter;
import org.springframework.security.web.header.writers.CacheControlHeadersWriter;
import org.springframework.security.web.header.writers.DelegatingRequestMatcherHeaderWriter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.NegatedRequestMatcher;
import org.springframework.security.web.util.matcher.OrRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;

/**
 * This is applicationContext-security.xml for spring-boot and war (specifically, security:http name="webApiHttpSecurity")
 *
 * Used by spring's WebSecurityConfigurerAdapter.
 *
 * This is intended to be hooked in by default using spring.factories, so that web-api adds itself in to config.
 * <p>
 * This replaces the XML configuration that starts: <security:http name="webApiHttpSecurity"
 * Docs from xml:
 *      A separate /api/ http section to ensure that /api returns 403, not the login page (see 71d7085f4)
 *      but otherwise we want to properties of the main url security
 */
@Order(80)
@EnableWebSecurity
public class WebApiEndpointSecurityConfigurer extends WebSecurityConfigurerAdapter { // Must extend this to get multiple HttpSecurityInstances for different paths
    // TODO spnego / activedirectory

    // TODO possibly turn off the defaults /css/**, /js/**, /images/** and **/favicon.ico
    //  see https://docs.spring.io/spring-boot/docs/1.3.0.M3/reference/html/boot-features-security.html#boot-features-security

    @Value("${ecco.api.basePath:/api}")
    private String apiBasePath; // default for servlet. Need to add in application.properties as /api for Spring Boot

    @Override
    public void configure(HttpSecurity http) throws Exception {

        //@formatter:off
        http
            //.authenticationManager()
            //.authenticationProvider(eccoAuthenticationProvider)
            // NOTE: this may still need to be relative to the context path (i.e. /ecco-war/), not the servlet path (/ecco-war/api/)
            .antMatcher(apiBasePath + "/**")
            // Allow authorization header in preflight
            // see https://www.baeldung.com/spring-security-cors-preflight
            .cors()
        .and()
            .antMatcher(apiBasePath + "/**")
                .sessionManagement()
                    .sessionCreationPolicy(SessionCreationPolicy.NEVER)
                    .sessionFixation().none() // we have a custom filter
        .and()
            .headers()
                .cacheControl().disable()
                .addHeaderWriter(selectiveCacheControlHeaderWriter())
                .xssProtection().and()
                .frameOptions().and()
        .and()
            .csrf().disable()
            .anonymous().authorities("ROLE_ANONYMOUS").key("doesNotMatter").principal("anonymousUser")
        .and()
            .authorizeRequests()
                .antMatchers(
                        apiBasePath + "/login_error/",
                        apiBasePath + "/p/**",
                        apiBasePath + "/config/public/",
                        apiBasePath + "/config/global", // is also public via api/inbound/config/global (see InboundBaseController.java / repair.tsx)
                        apiBasePath + "/contacts/sms/**", // TODO inbound sms should be /api/hooks/sms etc
                        apiBasePath + "/externalsystems/**",
                        apiBasePath + "/images/logo/**",
                        apiBasePath + "/images/qrcode/**",
                        apiBasePath + "/inbound/**",
                        apiBasePath + "/secure/command/",
                        apiBasePath + "/secure/query/**",
                        apiBasePath + "/stats/**",
                        apiBasePath + "/config/messages"
                        ).permitAll()

                // An API user logging in using the form needs /keys access
                // otherwise the Basic Auth fails, but it appears okay because
                // the form login succeeds.
                // Giving an API user ROLE_USER is good though, its designed just for access.
                .antMatchers(
                        apiBasePath + "/keys/**",
                        apiBasePath + "/config/user/",
                        apiBasePath + "/self/**"
                ).hasAnyRole("USER")

                .antMatchers(apiBasePath + "/export/**")
                    .hasRole("EXPORT")

                .antMatchers(apiBasePath + "/portal/**").hasAnyRole("USER")

                .antMatchers(apiBasePath + "/reportDef/**").hasAnyRole("STAFF","REPORTS")
                .antMatchers(apiBasePath + "/reports/**").hasAnyRole("STAFF","REPORTS")
                .antMatchers(HttpMethod.GET, apiBasePath + "/service/**").hasAnyRole("STAFF","REPORTS","ADMIN")
                .antMatchers(HttpMethod.GET, apiBasePath + "/project/**").hasAnyRole("STAFF","REPORTS","ADMIN")
                .antMatchers(HttpMethod.GET, apiBasePath + "/serviceCategorisations/**").hasAnyRole("STAFF","ADMIN")
                .antMatchers(HttpMethod.GET, apiBasePath + "/servicesProjects/**").hasAnyRole("STAFF","ADMIN")
                .antMatchers(HttpMethod.GET, apiBasePath + "/projects/**").hasAnyRole("STAFF","ADMIN")
                .antMatchers(HttpMethod.GET, apiBasePath + "/outcomes/**").hasAnyRole("STAFF","ADMIN")
                .antMatchers(HttpMethod.GET, apiBasePath + "/flags/**").hasAnyRole("STAFF","ADMIN")
                .antMatchers(HttpMethod.GET, apiBasePath + "/ldap/**").hasAnyRole("STAFF","ADMIN")
                .antMatchers(HttpMethod.GET, apiBasePath + "/service-recipient/**").hasAnyRole("STAFF","REPORTS")
                .antMatchers(HttpMethod.GET, apiBasePath + "/referrals/**").hasAnyRole("STAFF","REPORTS")
                .antMatchers(HttpMethod.GET, apiBasePath + "/clients/**").hasAnyRole("STAFF","REPORTS")
                .antMatchers(HttpMethod.GET, apiBasePath + "/contacts/**").hasAnyRole("STAFF","REPORTS")
                .antMatchers(HttpMethod.GET, apiBasePath + "/company/**").hasAnyRole("STAFF","REPORTS")
                .antMatchers(HttpMethod.GET, apiBasePath + "/agencies/**").hasAnyRole("STAFF","REPORTS")

                .antMatchers(apiBasePath + "/cache/**").hasRole("ADMIN")
                .antMatchers(apiBasePath + "/groups/").hasRole("ADMINLOGIN")
                .antMatchers(apiBasePath + "/users/**").hasRole("ADMINLOGIN")
                .antMatchers(apiBasePath + "/acls/**").hasRole("ADMINLOGIN") // match the controller - else we'd need default api access (/** below)
                .antMatchers(apiBasePath + "/scheduler/**").hasRole("ADMINROTA")
                .antMatchers(apiBasePath + "/incidents/**").hasRole("INCIDENTS")
                .antMatchers(apiBasePath + "/repairs/**").hasRole("REPAIRS")
                .antMatchers(apiBasePath + "/managedvoids/**").hasRole("MANAGEDVOIDS") // TOOD BUILDINGS ?
                .antMatchers(apiBasePath + "/notifications/**").hasRole("USER")
                .antMatchers(apiBasePath + "/occupancy/**").hasRole("USER") // TOOD BUILDINGS ?

                // NB this is a catch-all for /api
                .antMatchers(apiBasePath + "/**").hasRole("STAFF")

        .and()
            .httpBasic()
            // OR .addFilter(customBasicAuthenticationFilter() which can extend BasicAuthenticationFilter and have own config)
                .authenticationEntryPoint(authenticationEntryPoint())
                //.configure()

        .and()
            .exceptionHandling()
                .authenticationEntryPoint(authenticationEntryPoint())
                .accessDeniedHandler(exceptionHandling())
        ;

    }

    private AccessDeniedHandler exceptionHandling() {
        return new WebApiAccessDeniedHandler();
    }

    private AuthenticationEntryPoint authenticationEntryPoint() {
        return new WebApiAuthenticationEntryPoint();
    }

    private HeaderWriter selectiveCacheControlHeaderWriter() {

        RequestMatcher paths = new OrRequestMatcher(
                new AntPathRequestMatcher("/r/**"), // A different writer would probably be the case for resources config .. against a different config
                new AntPathRequestMatcher(apiBasePath + "/**")
        );

        return new DelegatingRequestMatcherHeaderWriter(
                new NegatedRequestMatcher(paths),
                new CacheControlHeadersWriter());
    }

}
