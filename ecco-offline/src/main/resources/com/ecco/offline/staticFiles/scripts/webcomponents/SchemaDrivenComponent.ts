import {HateoasResource} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {implementInterface, isJsonSchemaDto, JsonSchemaDto, ObjectSchemaDto} from "ecco-dto";
import URI = require("URI");

/** A SchemaDrivenComponent is a component that can be supplied with a schema URL and from there use that
 * to dynamically render the control based on the schema. */
abstract class SchemaDrivenComponent extends HTMLElement {

    /** The data that the schema describes - if loaded */
    private _data: HateoasResource;
    private _schema: ObjectSchemaDto;
    private _title: string;
    private _description: string;

    /** gets the component schema */
    get data() {
        return this._data;
    }

    set data(value) {
        this._data = value;
    }

    /** URI of the schema */
    get src(): URI.URI {
        return this.getAttribute('src') ? URI(this.getAttribute('src')) : null;
    }

    set src(src: URI.URI) {
        this.setAttribute('src', src.href());
    }

    /** gets the component schema */
    get schema() {
        return this._schema;
    }

    set schema(value) {
        this._schema = value;
    }

    get schemaObject() {
        return implementInterface(this._schema).asObjectSchema();
    }

    /** @return {string} gets the title */
    override get title() {
        return this._title || '';
    }

    /** @param {string} value, set title */
    override set title(value) {
        this._title = value || '';
    }

    /** @return {string} gets the description */
    get description() {
        return this._description || '';
    }

    /** @param {string} value, set the description */
    set description(value) {
        this._description = value || '';
    }

    attachedCallback() {
        if (this.src) {
            this.loadSrc(this.src.href());
        }
        else {
            this.render();
        }
    }

    attributeChangedCallback(attrName: string, oldVal: string, newVal: string): void {
        if (attrName === 'src' && oldVal !== newVal) {
            this.loadSrc(this.src.href());
        }
    }

    public createdCallback() {
        console.log("created");
    }

    private loadSrc(href: string) {
        console.debug(`loading schema/data from ${href}`);

        apiClient.get<JsonSchemaDto|HateoasResource>(URI(href)).then( schemaOrData => {
            if (isJsonSchemaDto(schemaOrData)) {
                this.schema = implementInterface(schemaOrData).asObjectSchema();
                this.schemaUpdated();
                this.render();
            }
            else {
                this.data = schemaOrData;
                // Render now, or load the schema
                if (this.schema) {
                    this.render();
                }
                else {
                    // load the schema and the try again
                    const describedby = this.data.links.filter(link => link.rel === 'describedby');
                    if (describedby.length > 0) {
                        const schemaHref = describedby[0].href;
                        this.loadSrc(schemaHref);
                    }
                    else {
                        console.error("No describedby link found in data.links")
                    }
                }

            }
        })
    }

    protected schemaUpdated() {
        console.debug("schemaUpdated: ", this.schema);
    }

    protected abstract render(): void;

}
export = SchemaDrivenComponent;