import * as React from "react"
import {ClassAttributes} from "react"
import {Grid} from "@eccosolutions/ecco-mui";
import {CommandQueue, CommandSource} from "ecco-commands";
import {
    datePickerIso8601Input,
    dropdownList,
    possiblyModalForm,
    textInput
} from "ecco-components-core";
import {
    AsyncServiceRecipientWithEntities,
    AsyncSessionData,
    CommandForm,
    CommandSubform,
    withCommandForm
} from "ecco-components";
import {SessionData} from "ecco-dto";
import {Client} from "ecco-dto/client-dto";

import {ClientResidenceUpdateCommand} from "ecco-commands";

const ClientResidenceEditor = (props: {serviceRecipientId: number, taskHandle: string, formRef: (c: ClientResidence) => void}) =>
        withCommandForm(commandForm =>
            possiblyModalForm(
                "client details",
                true, true,
                () => commandForm.cancelForm(),
                () => commandForm.submitForm(),
                false, // TODO could emitChangesTo and see if there are any commands
                false,
                getClientResidenceSubform(props.serviceRecipientId, props.taskHandle, props.formRef, commandForm)
            )
        );

/**
 *
 * formRef must extend CommandSource.  Used to emitChangesTo(cmdQ) when submitting. Could also ask hasChanges()...
 */
export function getClientResidenceSubform(serviceRecipientId: number,
                                          taskHandle: string,
                                          formRef: (c: ClientResidence) => void,
                                          commandForm?: CommandForm | undefined) {
    return <AsyncServiceRecipientWithEntities.Resolved>{workflowContext =>
            <AsyncSessionData.Resolved>{sessionData =>
                <React.Fragment>
                <ClientResidence
                    ref={formRef}
                    serviceRecipientId={serviceRecipientId}
                    client={workflowContext.client}
                    taskHandle={taskHandle}
                    readOnly={!sessionData.hasRoleReferralEdit()}
                    commandForm={commandForm}
                    sessionData={sessionData}
                />
                </React.Fragment>
            }</AsyncSessionData.Resolved>
        }</AsyncServiceRecipientWithEntities.Resolved>;
}


interface Props extends ClassAttributes<ClientResidence> {
    readOnly: boolean;
    serviceRecipientId: number;
    client: Client;
    taskHandle: string;
    sessionData: SessionData;
}

type ClientField = keyof Client;

interface State {
    client: Client;
    requiredFields: ClientField[];
    optionalFields: ClientField[];
}

// move outside the render, else we get a new one per character
const Entry = (props) => <Grid item sm={6} xs={12} >{props.children}</Grid>;

export class ClientResidence extends CommandSubform<Props, State> implements CommandSource {
    //private clientAddressLocationEditor: ClientAddressLocationEditor = null;

    constructor(props) {
        super(props);
        const client = this.props.client;

        const requiredFields = this.props.sessionData.getSettingAsArray('com.ecco.forms:CLIENT_DETAIL_REQUIRED_FIELDS') as ClientField[];
        const optionalFields = this.props.sessionData.getSettingAsArray('com.ecco.forms:CLIENT_DETAIL_OPTIONAL_FIELDS') as ClientField[];

        this.state = {
            client: client,
            requiredFields,
            optionalFields
        };
    }

    emitChangesTo(commandQueue: CommandQueue) {
        this.queueClientResidenceCommand(commandQueue);
    }

    protected queueClientResidenceCommand(commandQueue: CommandQueue) {
        const cmd = new ClientResidenceUpdateCommand(this.props.serviceRecipientId, this.props.taskHandle);
            // .changeStartDate(todo, this.state.startDate);

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }

        //this.clientAddressLocationEditor.
    }

    render() {
        const clientStateSetter = (client: Client) => this.setState({client});

        return (
            <div className="container-fluid v-gap-15">
                <Grid container>
                    <Entry>
                        {textInput("firstName", "first name", clientStateSetter, this.state.client, undefined, this.props.readOnly,
                        true)}
                    </Entry>
                    <Entry>
                        {textInput("lastName", "last name", clientStateSetter, this.state.client, undefined, this.props.readOnly,
                        true)}
                    </Entry>
                    <Entry>
                        {datePickerIso8601Input("birthDate", "birth date", clientStateSetter, this.state.client, this.props.readOnly,
                            this.isRequired('birthDate'))}
                    </Entry>
                    <Entry>
                        {dropdownList("gender", clientStateSetter, this.state.client, "genderId",
                            this.props.sessionData.getGenderList(),
                             undefined, this.props.readOnly, this.isRequired('genderId'))}
                    </Entry>
                    {this.isOptional('genderAtBirthId') ?
                        <Entry>
                            {dropdownList("gender at birth", clientStateSetter, this.state.client, "genderAtBirthId",
                                this.props.sessionData.getGenderAtBirthList(),
                                undefined, this.props.readOnly, this.isRequired('genderAtBirthId'))}
                        </Entry>
                        : null
                    }
                    {this.isOptional('firstLanguageId') ?
                        <Entry>
                            {dropdownList("first language", clientStateSetter, this.state.client, "firstLanguageId",
                                this.props.sessionData.getLanguageList(),
                                undefined, this.props.readOnly, this.isRequired('firstLanguageId'))}
                        </Entry>
                        : null
                    }
                    {this.isOptional('ethnicOriginId') ?
                        <Entry>
                            {dropdownList("ethnic origin", clientStateSetter, this.state.client, "ethnicOriginId",
                                this.props.sessionData.getEthnicOriginList(),
                                undefined, this.props.readOnly, this.isRequired('ethnicOriginId'))}
                        </Entry>
                        : null
                    }
                    {this.isOptional('nationalityId') ?
                        <Entry>
                            {dropdownList("nationality", clientStateSetter, this.state.client, "nationalityId",
                                this.props.sessionData.getNationalityList(),
                                undefined, this.props.readOnly, this.isRequired('nationalityId'))}
                        </Entry>
                        : null
                    }
                    {this.isOptional('maritalStatusId') ?
                        <Entry>
                            {dropdownList("marital status", clientStateSetter, this.state.client, "maritalStatusId",
                                this.props.sessionData.getMaritalStatusList(),
                                undefined, this.props.readOnly, this.isRequired('maritalStatusId'))}
                        </Entry>
                        : null
                    }
                    {this.isOptional('religionId') ?
                        <Entry>
                            {dropdownList("religion", clientStateSetter, this.state.client, "religionId",
                                this.props.sessionData.getReligionList(),
                                undefined, this.props.readOnly, this.isRequired('religionId'))}
                        </Entry>
                        : null
                    }
                    {this.isOptional('disabilityId') ?
                        <Entry>
                            {dropdownList("disability", clientStateSetter, this.state.client, "disabilityId",
                                this.props.sessionData.getDisabilityList(),
                                undefined, this.props.readOnly, this.isRequired('disabilityId'))}
                        </Entry>
                        : null
                    }
                    {this.isOptional('sexualOrientationId') ?
                        <Entry>
                            {dropdownList("sexual orientation", clientStateSetter, this.state.client, "sexualOrientationId",
                                this.props.sessionData.getSexualOrientationList(),
                                undefined, this.props.readOnly, this.isRequired('sexualOrientationId'))}
                        </Entry>
                        : null
                    }
                    {this.isOptional('ni') ?
                        <Entry>
                            {textInput("ni", "national insurance", clientStateSetter, this.state.client, undefined, this.props.readOnly,
                                this.isRequired('ni'))}
                        </Entry>
                        : null
                    }
                    {this.isOptional('housingBenefit') ?
                        <Entry>
                            {textInput("housingBenefit", "housing benefit", clientStateSetter, this.state.client, undefined, this.props.readOnly,
                                this.isRequired('housingBenefit'))}
                        </Entry>
                        : null
                    }
                    {this.isOptional('nhs') ?
                        <Entry>
                            {textInput("nhs", "nhs number", clientStateSetter, this.state.client, undefined, this.props.readOnly,
                                this.isRequired('nhs'))}
                        </Entry>
                        : null
                    }
                </Grid>
            </div>
        );
    }

    private isRequired(fieldName: keyof Client) {
        return this.state.requiredFields.indexOf(fieldName) >= 0;
    }

    private isOptional(fieldName: keyof Client) {
        return this.state.optionalFields.indexOf(fieldName) >= 0;
    }

}
export default ClientResidenceEditor;
