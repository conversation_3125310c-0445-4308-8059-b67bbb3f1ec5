import $ = require("jquery");

import BaseAsyncDataControl = require("../../controls/BaseAsyncDataControl");
import SelectList = require("../../controls/SelectList");
import {EccoDateTime} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import * as dto from "ecco-dto/group-support-dto";
import {GroupSupportAjaxRepository} from "ecco-dto";
import {getFeatureConfigRepository} from "ecco-offline-data";
import {SessionData} from "ecco-dto";

const repository = new GroupSupportAjaxRepository(apiClient);

class BackingData {
    constructor(public sessionData: SessionData, public activityTypes: dto.ActivityTypeDto[], public nextActivities: dto.GroupActivityDto[]) {
    }
}

/**
 * This is the pure code version of what GroupSupportView.ts does when it enhances a page
 */
class ActivityTypeWithDemandSelectorControl extends BaseAsyncDataControl<BackingData | null> {

    private serviceId: number;

    private activityTypeId: number | null;

    private selectList = new SelectList("groupSupportActivityType").withEmptyEntryText("").withEmptyEntryValue("");

    constructor(private onActivityTypeSelected?: (val: number | null) => void) {
        super();
        this.selectList.element().change( event => this.onSelected(event) );
    }

    protected fetchViewData(): Promise<BackingData | null> {
        if (!this.serviceId) {
            return Promise.resolve(null);
        }
        const sdQ = getFeatureConfigRepository().getSessionData();
        const activityTypes = repository.findActivityTypesByServiceId(this.serviceId);
        const activities = repository.findNextScheduledActivitiesByServiceId(this.serviceId);

        return Promise.all([sdQ, activityTypes, activities])
            .then(([sd, activityTypes, nextActivities]) => {
                return new BackingData(sd, activityTypes, nextActivities);
            })
    }

    protected render(data: BackingData | null) {
        this.selectList.clear();
        this.element()
            .empty()
            .append(this.selectList.element());

        if (!data) {
            return;
        } else if (data.activityTypes.length == 0 && data.nextActivities.length == 0) {
            this.element()
                .empty()
                .append('<span class="form-control-text">no activities configured for this service</span>');
            return;
        }

        const activityTypes = data.activityTypes.sort((a, b) => a.name.localeCompare(b.name));

        const typesMinusScheduled = activityTypes.filter(activityType => {

            for (let i = 0; i < data.nextActivities.length; i++) {
                if (data.nextActivities[i].activityTypeId == activityType.id) {
                    // TODO we've orphaned this table, so this is useless currently
                    //data.nextActivities[i].activityType.numReferrals = activityType.numReferrals; // cos it wasn't provided as part of this entity (HACK)
                    return false;
                }
            }
            return true;
            // could be as follows if we got demand into nextActivities: return !nextActivities.some( (activity) => activity.activityType.id == type.id )
        });

        const noDemand = typesMinusScheduled.filter(activityType => activityType.numReferrals == 0);
        const hasDemand = typesMinusScheduled.filter(activityType => activityType.numReferrals != 0);

        hasDemand.length > 0 && this.selectList.addOptionGroupFromList("no future events scheduled", hasDemand,
            idName => this.entryForActivityType(idName),
            idName => idName.id == this.activityTypeId);

        data.nextActivities.length > 0 && this.selectList.addOptionGroupFromList("events already scheduled", data.nextActivities,
            activity => this.entryForActivity(activity, data.sessionData),
            idName => idName.activityTypeId == this.activityTypeId);

        if (noDemand.length == activityTypes.length) {
            this.selectList.populateFromList(activityTypes,
                idName => this.entryForActivityType(idName),
                idName => idName.id == this.activityTypeId);
        } else {
            noDemand.length > 0 && this.selectList.addOptionGroupFromList("no interest at present", noDemand,
                idName => this.entryForActivityType(idName),
                idName => idName.id == this.activityTypeId);
        }
        this.selectList.element().change();
    }

    private entryForActivityType(idName: dto.ActivityTypeDto) {
        return { key: idName.id.toString(),
            value: idName.name + ((idName.numReferrals > 0) ? " (interest: " + idName.numReferrals + ")" : "")};
    }

    private entryForActivity(activity: dto.GroupActivityDto, sessionData: SessionData) {
        const label = this.annotateWithDemand(sessionData.getListDefinitionEntryById(activity.activityTypeId)?.getDisplayName(),
            EccoDateTime.parseIso8601IgnoringTimezone(activity.startDateTime),
            999, activity.capacity, 0);// activity.activityType.numReferrals);

        return { key: activity.activityTypeId.toString(), value: label};
    }

    private annotateWithDemand(name: string, nextScheduled: EccoDateTime, placesTaken: number, placesAvail: number,
        activityDemand: number): string {

        if (!nextScheduled) {
            return name + " (interest: " + activityDemand + ")";
        }
        return name + " ( " + nextScheduled.formatDatePretty() + ": " + /*placesTaken + "/" +*/ placesAvail
            + " places, interest: " + activityDemand + ")";
    }

    public loadWithService(serviceId: number, activityTypeId: number) {
        this.serviceId = serviceId;
        this.activityTypeId = activityTypeId;
        super.load();
    }

    private onSelected(event: $.BaseJQueryEventObject) {
        if (this.onActivityTypeSelected) {
            this.onActivityTypeSelected(this.getSelectedId());
        }
    }

    public getSelectedId(): number | null {
        this.activityTypeId = this.selectList.val() && parseInt(this.selectList.val()) || null;
        return this.activityTypeId;
    }

    public setSelectedId(activityTypeId: number | null) {
        this.selectList.setVal(activityTypeId ? activityTypeId.toString() : "");
        this.activityTypeId = activityTypeId;
    }

    public selectElement(): $.JQuery {
        return this.selectList.element();
    }
}
export = ActivityTypeWithDemandSelectorControl;
