import $ = require("jquery");

import BaseAsyncListControl = require("../controls/BaseAsyncListControl");
import BaseListEntryControl = require("../controls/BaseListEntryControl");
import {apiClient} from "ecco-components";
import {ActivityTypeDto} from "ecco-dto";
import {GroupSupportActivityTypeEdit} from "./GroupSupportActivityTypeEdit";
import {GroupSupportAjaxRepository} from "ecco-dto";

const repository: GroupSupportAjaxRepository = new GroupSupportAjaxRepository(apiClient.withCachePeriod(0));


class EntryControl extends BaseListEntryControl<ActivityTypeDto> {

    constructor(data: ActivityTypeDto) {
        super(data, "fa fa-pencil", data && data.disabled ? "disabled" : undefined);
    }

    protected administerEntry(): void {
        GroupSupportActivityTypeEdit.showInModal(this.entry.id);
    }

    protected getEditElement(): $.JQuery {
        return $("<div>").css("opacity", this.entry.disabled ? "0.6" : "1").addClass("container-fluid").append($("<div>").addClass("row"))
            .append(
                $("<div>").addClass("col-xs-9").append(
                    $("<span>").css("font-size", "0.8em").text(this.entry.name)
                )
            )
            .append(
                $("<div>").addClass("col-xs-3").append(
                    this.entry.disabled ? 'disabled ' : ''
                )
            );
    }

    protected getEntryIconClass(): string {
        return "fa fa-list";
    }
}

class GroupSupportActivityTypeListControl extends BaseAsyncListControl<ActivityTypeDto> {

    constructor() {
        super("add new type", "no types defined", "fa fa-list");
    }

    protected fetchViewData(): Promise<ActivityTypeDto[]> {
        return repository.findActivityTypes();
    }
    protected createItemControl(data: ActivityTypeDto) {
        return new EntryControl(data);
    }

    protected addNewEntity() {
        GroupSupportActivityTypeEdit.showInModal(null);
    };

}
export = GroupSupportActivityTypeListControl;
