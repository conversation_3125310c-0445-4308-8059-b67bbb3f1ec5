/** Returns a unique, persistent identifier for the specified object, which
 * may be any value of any type, including null and undefined.
 *
 * If the object is not a primitive type or null, then this function will
 * add a property to the object with a name similar to $ecco$id$1234567890.
 * Modifying that property in any way will cause this function to behave
 * incorrectly for the corresponding object.
 *
 * The identifier will begin with "$" to ensure that it can be used as an
 * object property name without conflicting with any troublesome property
 * names such as __proto__ or hasOwnProperty. */
function objectId(object: any): string {
    const f = objectIdByType[typeof object];
    if (f) {
        return f(object);
    } else {
        return objectIdByType["object"](object);
    }
}

const idRecordKey = "$dictionary$id$" + Date.now();
let nextId = 0;

class IdRecord {
    constructor(public id:string, public object:any) {
    }
}

const objectIdByType: { [type: string]: (value: any) => string } = {
    undefined: () => "$u",
    boolean: (object: boolean) => "$b$" + object,
    number: (object: number) => "$n$" + object,
    string: (object: string) => "$s$" + object,
    object: (object: any) => {
        if (object === null) {
            return "$n";
        }

        const idRecord: IdRecord = object[idRecordKey];
        if (idRecord && idRecord.object === object) {
            return idRecord.id;
        } else {
            const id:string = "$o$" + (nextId++);
            object[idRecordKey] = new IdRecord(id, object);
            return id;
        }
    }
};

class KeyValuePair<TKey, TValue> {
    constructor(public key: TKey, public value: TValue) {
    }
}

/**
 * A dictionary/map data type that safely supports keys of arbitrary types.
 *
 * This dictionary is guaranteed to behave correctly even in the presence
 * of troublesome JavaScript programming practices, such as adding
 * properties to Object.prototype. In particular it is safe to use any
 * arbitrary string as a key, even strings that can be troublesome when
 * used as property names, such as "__proto__" or "hasOwnProperty".
 *
 * If an object is not a primitive type or null, then using it as a key
 * will add a property to the object with a name similar to $ecco$id$1234567890.
 * This is necessary to uniquely identify the object. Modifying that
 * property in any way will cause the dictionary to behave incorrectly for
 * that key.
 *
 * @deprecated Use `Map<TKey, TValue>` instead.
 */
class Dictionary<TKey, TValue> {
    private keyValuePairs: { [id: string]: KeyValuePair<TKey, TValue>; } = {};

    private getKeyValuePair(key: TKey) {
        return this.keyValuePairs[objectId(key)];
    }

    /** Tests if the specified key is set in this dictionary. */
    public containsKey(key: TKey) {
        return !!this.getKeyValuePair(key);
    }

    /** Gets the value corresponding to the specified key, or undefined
     * if the specified key is not set. */
    public get(key: TKey): TValue | undefined {
        const keyValuePair = this.getKeyValuePair(key);
        if (keyValuePair) {
            return keyValuePair.value;
        } else {
            return undefined;
        }
    }

    /** Gets the value corresponding to the specified key.
     *
     * Returns the Dictionary for chaining. */
    public set(key: TKey, value: TValue) {
        this.keyValuePairs[objectId(key)] = new KeyValuePair(key, value);
        return this;
    }

    /** Removes the specified key and corresponding value from the
     * dictionary.
     *
     * Returns the Dictionary for chaining. */
    public remove(key: TKey) {
        delete this.keyValuePairs[objectId(key)];
        return this;
    }

    /** Calls the specified callback for each key/value pair stored by the
     * dictionary.
     *
     * Returns the Dictionary for chaining. */
    public forEach(callback: (key: TKey, value: TValue) => void) {
        for (let id in this.keyValuePairs) {
            const keyValuePair = this.keyValuePairs[id];
            if (keyValuePair instanceof KeyValuePair) {
                callback(keyValuePair.key, keyValuePair.value);
            }
        }
        return this;
    }
}

export = Dictionary;