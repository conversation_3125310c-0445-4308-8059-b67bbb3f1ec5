import moment = require("moment");
import FormEvent = React.FormEvent;
import * as React from "react"
import {ReactNode} from "react"
import {
    AddressLocation,
    apiClient,
    ninoFn,
    update,
    UpdateSpec
} from "ecco-components";
import {possiblyModalForm} from "ecco-components-core";
import {FlashScope, isEmpty, PartialTo} from "@eccosolutions/ecco-common";
import {
    ClientAjaxRepository,
    SessionData
} from "ecco-dto";
import {Client} from "ecco-dto/client-dto";
import {Alert, Col, ControlLabel, FormControl, FormGroup, Row} from "react-bootstrap";
import {dropdownList, FieldGroup} from "../../components/ComponentUtils";
import {default as DateInput} from "../../components/DateInput";

type StringChangeEvent = FormEvent<any>;

/**
 * FIXME: Make the command/non-command bit a controller that wraps the same stateless form component - then roll out to staff and building versions
 *
 * Note differing layout between New and edit versions - and extract that as a component with same interface
 * Also one may have custom form the other not
 * NON-command-based CREATING of a client
 */

interface Props extends React.ClassAttributes<ClientDetailNew> {
    sessionData: SessionData;
    client?: Client | undefined;
    show?: boolean | undefined;
    showAsModal?: boolean | undefined;
    onSave?: ((client: Client) => void) | undefined;
    onCancel?: (() => void) | undefined;
}

type AlertState = { [key:string]: string};

type ClientField = keyof Client;

interface State {
    client: Client;
    errors: {[P in keyof Client]?: string};
    requiredFields: ClientField[];
    optionalFields: ClientField[];
    alerts: AlertState;
    editingAddress: boolean // if there is some address provided, then we must be editing
}

class ClientDetailNew extends React.Component<Props, State> {
    private saveTimer;
    private repository: ClientAjaxRepository = new ClientAjaxRepository(apiClient);

    private addressLocation: AddressLocation | null = null;

    private optionalFieldCounter;
    private rowObjStart = {
        labelClassName: 'col-xs-2 hidden-md hidden-lg',
        wrapperClassName: "col-xs-10 col-md-offset-2 col-md-5",
        clearClassName: "clearfix visible-sm visible-xs"
    };

    private rowObjEnd = {
        labelClassName: 'col-xs-2 hidden-md hidden-lg',
        wrapperClassName: "col-xs-10 col-md-5",
        clearClassName: "clearfix"
    };

    constructor(props) {
        super(props);

        const client = this.props.client || this.getNewClient();

        const currentFirstName = FlashScope.get("currentFirstName");
        if (currentFirstName && !client.firstName) {
            client.firstName = currentFirstName;
            client.lastName = FlashScope.get("currentLastName")
        }

        this.state = {
            client,
            errors: {},
            requiredFields: [],
            optionalFields: [],
            alerts: {},
            editingAddress: client.addressedLocationId != null || client.address != null // if there is some address provided, then we must be editing
        };
        // Validate the client we got provided.
        // Won't be correct on basis of required fields until after session data loaded
        (this.state as State).errors = this.validate(client); // cannot and should not use setState here
    }

    public override componentDidMount() {
        const {sessionData} = this.props;
        const requiredFields = sessionData.getSettingAsArray('com.ecco.forms:CLIENT_DETAIL_REQUIRED_FIELDS') as ClientField[];
        const optionalFields = sessionData.getSettingAsArray('com.ecco.forms:CLIENT_DETAIL_OPTIONAL_FIELDS') as ClientField[];
        this.setState({
            requiredFields,
            optionalFields,
            errors: this.validate(this.state.client)
        });
    }

    public override componentWillUnmount() {
        window.clearTimeout(this.saveTimer);
    }

    public override UNSAFE_componentWillReceiveProps(nextProps: Props) {
        if (this.props.client != nextProps.client) {
            const client = nextProps.client || this.getNewClient();
            const errors = this.validate(client);

            this.setState({
                client: client,
                errors: errors,
                alerts: {},
                editingAddress: client.addressedLocationId != null || client.address != null // if there is some address provided, then we must be editing
            });
        }
    }

    private getNewClient(): Client {
        return {discriminator: "client"} as Client;
    }

    // FIXME: This function cannot be made typesafe.
    private handleClientChange(updater: (value: any) => UpdateSpec<Client>, event) {
        const client = update(this.state.client, updater(event.target.value));
        const errors = this.validate(client);

        this.setState({
            client: client,
            errors: errors
        });
    }

    private handleFirstNameChange: (event: StringChangeEvent) => void
        = this.handleClientChange.bind(this, v => ({firstName: {$set: v}}));

    private handleLastNameChange: (event: StringChangeEvent) => void
        = this.handleClientChange.bind(this, v => ({lastName: {$set: v}}));

    private handleNiChange: (event: StringChangeEvent) => void
        = this.handleClientChange.bind(this, v => ({ni: {$set: v}}));

    private handleHousingBenefitChange: (event: StringChangeEvent) => void
        = this.handleClientChange.bind(this, v => ({housingBenefit: {$set: v}}));

    private handleNhsChange: (event: StringChangeEvent) => void
        = this.handleClientChange.bind(this, v => ({nhs: {$set: v}}));

    private handleAddressValidChange = (valid: boolean) => {
        const errors = this.validate(this.state.client); // create errors object
        if (!valid) {
            errors.address = 'required';
        }
        this.setState({
            errors: errors
        });
    };

    private handleBirthDayChange = (date: Date) => {
        const client = update(this.state.client, {birthDate: {$set: date && moment.utc(date).format('YYYY-MM-DD')}});
        const errors = this.validate(client);

        this.setState({
            client: client,
            errors: errors
        });
    };

    private handleClientPropertyChange = (event: StringChangeEvent, update: (v: string) => Client) => {
        const v: string = (event.target as HTMLInputElement).value;
        const client = update(v == "" ? undefined : v); // don't want null -> "" changes or attempt to set something as empty string
        const errors = this.validate(client);
        this.setState({
            client: client,
            errors: errors
        });
    };

    private clientStateSetter = (client: Client) => {
        const errors = this.validate(client);
        this.setState({errors, client});
    };


    private handleToggleClick = () => {
        // copy postcode between fields when toggling
        // FIXME: should call a setter that sets state or actually do via props...
        // if (this.state.editingAddress) {
        //     this.addressList.state.postCode = this.addressDetail.state.address.postcode;
        // } else {
        //     this.addressDetail.state.address.postcode = this.addressList.state.postCode;
        // }

        const client = update(this.state.client, {addressedLocationId: {$set: null}});
        const errors = this.validate(client);

        this.setState({
            client: client,
            errors: errors,
            editingAddress: !this.state.editingAddress
        });
    };

    private handleSaveClick = () => {
        // this is the point that a client is actually saved
        this.repository.saveClient(this.getClientWithUpdatedAddress())
        .then(response => {
            this.saveTimer = window.setTimeout(() => this.handleSaveTimeout(), 1500);

            this.setState({
                client: update(this.state.client, {clientId: {$set: response.id}}),
                alerts: {info: 'individual saved'}
            });
        }).catch(reason => {
            this.setState({alerts: {danger: 'failed to save client: '
                + (reason.reason && reason.reason.message ? reason.reason.message : reason.toString()) }});
        });
    };

    private handleSaveTimeout() {
        this.props.onSave && this.props.onSave(this.state.client);
        const client = this.getNewClient();

        this.setState({
            client: client,
            errors: this.validate(client),
            alerts: {},
            editingAddress: false
        });
    }

    private handleCancelClick = () => {
        this.props.onCancel && this.props.onCancel();

        const client = this.getNewClient();
        this.setState({
            client: client,
            errors: this.validate(client),
            alerts: {},
            editingAddress: false
        });
    };

    private getClientWithUpdatedAddress(): Client {
        if (this.addressLocation.hasAddressChange()) {
            // TODO: update via a callback to setState
            this.state.client.addressedLocationId = this.addressLocation.getAddressLocationId();
        }
        return this.state.client;
    }

    private validate(client: Client) {
        const errors: PartialTo<Client, string> = {};
        if (!client.firstName) errors.firstName = 'required';
        if (!client.lastName) errors.lastName = 'required';
        if (this.isRequired('birthDate') && !client.birthDate) errors.birthDate = 'required';

        if (this.isRequired('address') && !client.addressedLocationId) errors.addressedLocationId = 'required';

        this.state.requiredFields.map(opt => {
            if (this.isRequired(opt) && isEmpty(client[opt])) {
                errors[opt] = 'required';
            }
        });

        if (client.ni) {
            if (ninoFn(false, client.ni) == "error") {
                errors['ni'] = 'invalid';
            }
        }

        return errors;
    }

    private validationState(field: keyof Client) {
        return this.isRequired(field)
            ? this.state.errors[field] ? 'success' : 'error'
            : undefined;
    }

    private isRequired(fieldName: keyof Client) {
        return this.state.requiredFields.indexOf(fieldName) >= 0;
    }

    public isValid(): boolean {
        return Object.keys(this.state.errors).length == 0;
    }

    override render() {
        // This gets modified ongoingly during render so need to reset each render.
        this.optionalFieldCounter = 0;

        /* NOTE: the order of this in the JSX is important.
         * See https://facebook.github.io/react/docs/transferring-props.html */
        const AlertElement = Object.entries(this.state.alerts)
            .map(([style, message]) => (
            <Alert bsStyle={style.toString()}>{message}</Alert>
        ));

        const alerts = document.querySelectorAll("div.alert");
        alerts.length > 0 && alerts[0].scrollIntoView();

        const {sessionData} = this.props;
        const {client, errors} = this.state;

        // TODO: see worker.jsp and clientDetail.jspf optionalFields
        // // text: date: textbox: mothersFirstName, paris, militaryNumber, keyCode, externalSystemRef
        const ClientDetails: ReactNode = (
                <div>
                {AlertElement}
                <form className='form-responsive'>
                    <FormGroup
                        validationState={(client.firstName != undefined && errors.firstName)
                    || (client.lastName != undefined && errors.lastName) ? 'error': 'success'}>
                        <ControlLabel bsClass='col-xs-2'>name</ControlLabel>
                        <Col xs={10}>
                            <Row>
                                <Col xs={6}>
                                    <FormControl
                                        type="text"
                                        className="form-control"
                                        value={client.firstName || ''}
                                        placeholder='first name'
                                        autoComplete='off'
                                        onChange={this.handleFirstNameChange} />
                                </Col>
                                <Col xs={6}>
                                    <FormControl
                                        type="text"
                                        className="form-control"
                                        placeholder='last name'
                                        autoComplete='off'
                                        value={client.lastName || ''}
                                        onChange={this.handleLastNameChange} />
                                </Col>
                            </Row>
                        </Col>
                    </FormGroup>
                    <div className="clearfix"/>
                    <DateInput
                        name='birthDate'
                        label='birth date'
                        value={client.birthDate != null ? new Date(client.birthDate) : undefined}
                        onChange={this.handleBirthDayChange}
                        required={this.state.requiredFields.indexOf('birthDate') >= 0}
                    />
                    <div className="clearfix visible-sm visible-xs"/>

                    {dropdownList("gender", this.clientStateSetter, this.state.client, "genderId",
                        sessionData && sessionData.getGenderList(),
                        {labelClassName: 'col-xs-2 hidden-md hidden-lg', wrapperClassName: 'col-xs-10 col-md-3'},
                        "gender", false, this.isRequired('genderId'))}
                    <div className="clearfix"/>

                    <div>
                        <FieldGroup
                            type='text'
                            label='phone number'
                            labelClassName={this.rowObjStart.labelClassName}
                            wrapperClassName={this.rowObjStart.wrapperClassName}
                            placeholder='phone number'
                            value={client.phoneNumber}
                            onChange={(event: StringChangeEvent) => this.handleClientPropertyChange(event, (v) => update(this.state.client, {phoneNumber: {$set: v}}))}
                            validationState={this.validationState('phoneNumber')}/>
                        <div className="clearfix visible-sm visible-xs"/>
                    </div>

                    <div>
                        <FieldGroup
                            type='text'
                            label='mobile number'
                            labelClassName={this.rowObjEnd.labelClassName}
                            wrapperClassName={this.rowObjEnd.wrapperClassName}
                            placeholder='mobile number'
                            value={client.mobileNumber}
                            onChange={(event: StringChangeEvent) => this.handleClientPropertyChange(event, (v) => update(this.state.client, {mobileNumber: {$set: v}}))}
                            validationState={this.validationState('mobileNumber')}/>
                        <div className="clearfix"/>
                    </div>

                    <div>
                        <FieldGroup
                            type='text'
                            label='email'
                            labelClassName={this.rowObjStart.labelClassName}
                            wrapperClassName={this.rowObjStart.wrapperClassName}
                            placeholder='email'
                            value={client.email}
                            onChange={(event: StringChangeEvent) => this.handleClientPropertyChange(event, (v) => update(this.state.client, {email: {$set: v}}))}
                            validationState={this.validationState('email')}/>
                        <div className="clearfix visible-sm visible-xs"/>
                    </div>

                    <div className="clearfix"/>
                    {this.getOptionalField("firstLanguageId")}
                    {this.getOptionalField("ethnicOriginId")}
                    {this.getOptionalField("nationalityId")}
                    {this.getOptionalField("genderAtBirthId")}
                    {this.getOptionalField("maritalStatusId")}
                    {this.getOptionalField("religionId")}
                    {this.getOptionalField("disabilityId")}
                    {this.getOptionalField("sexualOrientationId")}
                    {this.getOptionalField("ni")}
                    {this.getOptionalField("housingBenefit")}
                    {this.getOptionalField("nhs")}
                </form>

                <div className='row'>
                    <div className='col-xs-10 col-xs-offset-2'>
                    <div className="page-header">
                    <AddressLocation
                        displayAddress={client.address}
                        showBuildings={this.props.sessionData.isEnabled("menu.buildings")}
                        ref={c => this.addressLocation = c}
                        handleAddressValidChange={this.handleAddressValidChange}
                    />

                    </div>
                    </div>
                </div>
                </div>
            );

        return possiblyModalForm("client details", this.props.showAsModal || false, this.props.show || false,
            this.handleCancelClick, this.handleSaveClick, !this.isValid(), false, ClientDetails);
    }

    private getOptionalField(fieldName: string) {
        const {sessionData} = this.props;
        const {client, optionalFields} = this.state;

        let rowObj = this.rowObjEnd;
        switch (fieldName) {
            case "firstLanguageId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("first language", this.clientStateSetter, this.state.client, "firstLanguageId",
                            sessionData && sessionData.getLanguageList(),
                            rowObj, "first language", false, this.isRequired('firstLanguageId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "ethnicOriginId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("ethnic origin", this.clientStateSetter, this.state.client, "ethnicOriginId",
                            sessionData && sessionData.getEthnicOriginList(),
                            rowObj, "ethnic origin", false, this.isRequired('ethnicOriginId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "nationalityId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("nationality", this.clientStateSetter, this.state.client, "nationalityId",
                            sessionData && sessionData.getNationalityList(),
                            rowObj, "nationality", false, this.isRequired('nationalityId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "maritalStatusId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("marital status", this.clientStateSetter, this.state.client, "maritalStatusId",
                            sessionData && sessionData.getMaritalStatusList(),
                            rowObj, "marital status", false, this.isRequired('maritalStatusId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "religionId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("religion", this.clientStateSetter, this.state.client, "religionId",
                            sessionData && sessionData.getReligionList(),
                            rowObj, "religion", false, this.isRequired('religionId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "disabilityId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("disability", this.clientStateSetter, this.state.client, "disabilityId",
                            sessionData && sessionData.getDisabilityList(),
                            rowObj, "disability", false, this.isRequired('disabilityId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "sexualOrientationId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("sexual orientation", this.clientStateSetter, this.state.client, "sexualOrientationId",
                            sessionData && sessionData.getSexualOrientationList(),
                            rowObj, "sexual orientation", false, this.isRequired('sexualOrientationId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "genderAtBirthId":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        {dropdownList("gender at birth", this.clientStateSetter, this.state.client, "genderAtBirthId",
                            sessionData && sessionData.getGenderAtBirthList(),
                            rowObj, "gender at birth", false, this.isRequired('genderAtBirthId'))}
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "ni":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        <FieldGroup
                            type='text'
                            label='national insurance'
                            labelClassName='col-xs-2 hidden-md hidden-lg'
                            wrapperClassName={rowObj.wrapperClassName}
                            placeholder='national insurance'
                            value={client.ni}
                            onChange={this.handleNiChange}
                            validationState={this.validationState('ni')}/>
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "housingBenefit":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        <FieldGroup
                            type='text'
                            label='housing benefit'
                            labelClassName='col-xs-2 hidden-md hidden-lg'
                            wrapperClassName={rowObj.wrapperClassName}
                            placeholder='housing benefit'
                            value={client.housingBenefit}
                            onChange={this.handleHousingBenefitChange}
                            validationState={this.validationState('housingBenefit')}/>
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;

            case "nhs":
                rowObj = this.optionalFieldCounter % 2 == 0 ? this.rowObjStart : this.rowObjEnd;
                this.optionalFieldCounter += optionalFields.indexOf(fieldName) >= 0 ? 1 : 0;
                return optionalFields.indexOf(fieldName) >= 0
                    ? (<div>
                        <FieldGroup
                            type='text'
                            label='nhs number'
                            labelClassName='col-xs-2 hidden-md hidden-lg'
                            wrapperClassName={rowObj.wrapperClassName}
                            placeholder='nhs number'
                            value={client.nhs}
                            onChange={this.handleNhsChange}
                            validationState={this.validationState('nhs')}/>
                        <div className={rowObj.clearClassName}/>
                    </div>)
                    : null;
        }
        return null;
    }

}

export default ClientDetailNew;
